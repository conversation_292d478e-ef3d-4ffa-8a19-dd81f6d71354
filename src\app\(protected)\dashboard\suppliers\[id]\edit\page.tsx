import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SupplierEditPage from "@/components/pages/dashboard/suppliers/edit/supplier-edit";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getSupplierById } from "@/actions/entities/suppliers";

export const metadata: Metadata = {
  title: "Edit Supplier | Kasir Online",
  description: "Edit informasi supplier",
};

interface SupplierEditProps {
  params: {
    id: string;
  };
}

const SupplierEdit = async ({ params }: SupplierEditProps) => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch supplier data
  const supplierResult = await getSupplierById(params.id);

  if (supplierResult.error || !supplierResult.supplier) {
    redirect("/dashboard/contacts?tab=supplier");
  }

  return (
    <DashboardLayout>
      <SupplierEditPage supplier={supplierResult.supplier} />
    </DashboardLayout>
  );
};

export default SupplierEdit;
