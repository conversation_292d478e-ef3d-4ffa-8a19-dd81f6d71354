import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import CustomerDetailPage from "@/components/pages/dashboard/customers/detail/customer-detail";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getCustomerById } from "@/actions/entities/customers";

export const metadata: Metadata = {
  title: "Detail Pelanggan | Kasir Online",
  description: "Detail informasi pelanggan",
};

interface CustomerDetailProps {
  params: {
    id: string;
  };
}

const CustomerDetail = async ({ params }: CustomerDetailProps) => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch customer data
  const customerResult = await getCustomerById(params.id);

  if (customerResult.error || !customerResult.customer) {
    redirect("/dashboard/contacts?tab=pelanggan");
  }

  return (
    <DashboardLayout>
      <CustomerDetailPage customer={customerResult.customer} />
    </DashboardLayout>
  );
};

export default CustomerDetail;
