import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import CustomerEditPage from "@/components/pages/dashboard/customers/edit/customer-edit";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getCustomerById } from "@/actions/entities/customers";

export const metadata: Metadata = {
  title: "Edit Pelanggan | Kasir Online",
  description: "Edit informasi pelanggan",
};

interface CustomerEditProps {
  params: {
    id: string;
  };
}

const CustomerEdit = async ({ params }: CustomerEditProps) => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch customer data
  const customerResult = await getCustomerById(params.id);

  if (customerResult.error || !customerResult.customer) {
    redirect("/dashboard/contacts?tab=pelanggan");
  }

  return (
    <DashboardLayout>
      <CustomerEditPage customer={customerResult.customer} />
    </DashboardLayout>
  );
};

export default CustomerEdit;
