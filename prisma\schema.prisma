// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// --- Authentication Models ---

// Define Role enum for user roles
enum Role {
  OWNER
  ADMIN
  CASHIER
}

// Define subscription plan types
enum SubscriptionPlan {
  FREE
  BASIC
  PRO
  ENTERPRISE
}

// Define payment status
enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  EXPIRED
  REFUNDED
}

model User {
  id            String    @id @default(cuid())
  name          String?
  username      String?   @unique
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String? // HASHED password!
  provider      String?   @default("credentials")
  role          Role      @default(OWNER) // Using enum Role
  token         String?   @unique
  companyId     String?   @unique // Company identifier with format IP000001, IP000002, etc.
  phone         String? // User's phone number
  bio           String?   @db.Text // User's bio or about me
  birthday      DateTime? // User's birthday
  lastLogin     DateTime? // Last login timestamp
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]

  // Subscription related
  currentPlan        SubscriptionPlan @default(FREE)
  subscriptionExpiry DateTime?
  subscriptions      Subscription[]
  payments           Payment[]

  // --- User's Own Data ---
  products   Product[] // User's products
  categories Category[] // User's categories
  suppliers  Supplier[] // User's suppliers
  customers  Customer[] // User's customers
  contacts   Contact[] // User's unified contacts
  sales      Sale[] // Sales recorded by this user
  purchases  Purchase[] // Purchases recorded by this user
  services   Service[] // Services recorded by this user
  units      Unit[] // Units created by this user

  // Owner-Employee relationship
  employees            Employee[] // Employees managed by this user (if OWNER)
  notifications        Notification[]
  notificationSettings NotificationSettings?

  @@map("users")
}

// Employee model for staff members managed by an owner
model Employee {
  id         String   @id @default(cuid())
  name       String
  employeeId String // Employee ID/username for login
  password   String // HASHED password
  role       Role     @default(CASHIER) // ADMIN or CASHIER
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relationship to owner
  ownerId       String // ID of the owner (User with OWNER role)
  ownerUsername String // Username of the owner for login purposes
  owner         User   @relation(fields: [ownerId], references: [id], onDelete: Cascade)

  // Employee's activities
  sales Sale[] // Sales recorded by this employee

  @@unique([ownerId, employeeId]) // Employee ID must be unique per owner
  @@index([ownerId])
  @@map("employees")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("sessions")
}

model VerificationToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())

  @@unique([email, token])
  @@map("verification_tokens")
}

// --- Business Logic Models (Scoped per User) ---

model Product {
  id            String   @id // Custom ID will be generated in the application code
  name          String
  description   String?
  sku           String? // Stock Keeping Unit - Made unique *per user* below
  price         Decimal  @db.Decimal(10, 2)
  discountPrice Decimal? @db.Decimal(10, 2) // Discount price (harga diskon)
  cost          Decimal? @db.Decimal(10, 2)
  stock         Int      @default(0)
  image         String? // URL to the product image stored in Vercel Blob
  weight        Int?     @default(0) // Weight in grams
  length        Decimal? @db.Decimal(10, 2) // Length in cm
  width         Decimal? @db.Decimal(10, 2) // Width in cm
  height        Decimal? @db.Decimal(10, 2) // Height in cm
  unit          String   @default("Pcs") // Unit of measurement (e.g., Pcs, Kg, Liter)
  unitId        String? // Foreign key for Unit
  tags          String[] // Array of tags for the product
  isDraft       Boolean  @default(false) // Whether the product is a draft
  hasVariants   Boolean  @default(false) // Whether the product has variants
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  userId        String // Foreign key to User
  user          User             @relation(fields: [userId], references: [id], onDelete: Cascade) // Relation to User
  saleItems     SaleItem[]
  purchaseItems PurchaseItem[]
  categoryId    String? // Foreign key for Category
  category      Category?        @relation(fields: [categoryId], references: [id]) // Relation to Category
  variants      ProductVariant[] // Relation to product variants
  unitModel     Unit?            @relation(fields: [unitId], references: [id])

  @@unique([userId, sku]) // SKU is unique for a specific user
  @@index([userId]) // Index for user lookup
  @@index([categoryId])
  @@map("products")
}

model Category {
  id        String   @id @default(cuid())
  name      String // Category name - Made unique *per user* below
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  userId   String // <<<< CORRECTED: Just the scalar foreign key type
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade) // <<<< CORRECTED: @relation is here
  products Product[]

  @@unique([userId, name]) // Category name is unique for a specific user
  @@index([userId]) // Index for user lookup
  @@map("categories")
}

model Unit {
  id        String   @id @default(cuid())
  name      String // Unit name - Made unique *per user* below
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  userId   String
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  products Product[] // Products using this unit

  @@unique([userId, name]) // Unit name is unique for a specific user
  @@index([userId]) // Index for user lookup
  @@map("units")
}

// Product Variant model for color variants
model ProductVariant {
  id        String   @id @default(cuid())
  sku       String? // Variant-specific SKU
  colorName String // Name of the color (e.g., "Red", "Blue")
  colorCode String // Hex color code (e.g., "#FF0000" for red)
  price     Decimal? @db.Decimal(10, 2) // Optional variant-specific price
  stock     Int      @default(0) // Stock for this specific variant
  image     String? // URL to the variant image
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  productId String // Foreign key to the parent product
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@map("product_variants")
}

model Sale {
  id                String   @id // Custom ID will be generated in the application code
  saleDate          DateTime @default(now())
  totalAmount       Decimal  @db.Decimal(12, 2)
  transactionNumber String? // Transaction number/reference
  invoiceRef        String? // Invoice reference number
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relationships
  userId     String // Foreign key to the User who made the sale
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items      SaleItem[] // List of items in this sale
  Employee   Employee?  @relation(fields: [employeeId], references: [id])
  employeeId String?

  @@index([userId])
  @@map("sales")
}

model SaleItem {
  id          String   @id @default(cuid())
  quantity    Int
  priceAtSale Decimal  @db.Decimal(10, 2)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  saleId    String // Foreign key to the Sale
  sale      Sale    @relation(fields: [saleId], references: [id], onDelete: Cascade)
  productId String // Foreign key to the Product sold
  product   Product @relation(fields: [productId], references: [id])

  @@index([saleId])
  @@index([productId])
  @@map("sale_items")
}

model Purchase {
  id                String    @id // Custom ID will be generated in the application code
  purchaseDate      DateTime  @default(now())
  totalAmount       Decimal   @db.Decimal(12, 2)
  invoiceRef        String?
  isDraft           Boolean   @default(false) // Whether the purchase is a draft
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  // New fields
  supplierEmail     String? // Email of the supplier
  transactionDate   DateTime  @default(now()) // Date of the transaction
  paymentDueDate    DateTime? // Due date for payment
  transactionNumber String? // Transaction number/reference
  tags              String[] // Array of tags for the purchase
  billingAddress    String? // Billing address
  memo              String?   @db.Text // Memo field
  lampiran          Json[] // Array of file objects with URL and filename

  // Relationships
  userId     String // Foreign key to the User who recorded the purchase
  user       User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  items      PurchaseItem[] // List of items in this purchase
  supplierId String? // Foreign key for optional supplier
  supplier   Supplier?      @relation(fields: [supplierId], references: [id])

  @@index([userId])
  @@index([supplierId])
  @@map("purchases")
}

model PurchaseItem {
  id             String   @id @default(cuid())
  quantity       Int
  costAtPurchase Decimal  @db.Decimal(10, 2)
  description    String? // Description of the item
  unit           String?  @default("Buah") // Unit of measurement (e.g., Buah, Kg, Liter)
  tax            String? // Tax information (e.g., PPN, PPh)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  purchaseId String // Foreign key to the Purchase
  purchase   Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)
  productId  String // Foreign key to the Product purchased
  product    Product  @relation(fields: [productId], references: [id])

  @@index([purchaseId])
  @@index([productId])
  @@map("purchase_items")
}

model Supplier {
  id          String   @id // Custom readable ID like SUP-000001
  name        String
  contactName String?
  email       String? // Made unique *per user* below
  phone       String?
  address     String?
  notes       String?  @db.Text
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  userId    String // <<<< CORRECTED: Just the scalar foreign key type
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade) // <<<< CORRECTED: @relation is here
  purchases Purchase[]

  @@unique([userId, email]) // Supplier email is unique for a specific user (if not null)
  @@index([userId]) // Index for user lookup
  @@map("suppliers")
}

model Customer {
  id          String   @id // Custom readable ID like CUS-000001
  name        String
  contactName String?
  email       String? // Made unique *per user* below
  phone       String?
  address     String?
  notes       String?  @db.Text
  NIK         String? // Indonesian National ID Number
  NPWP        String? // Indonesian Tax ID Number
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, email]) // Customer email is unique for a specific user (if not null)
  @@index([userId]) // Index for user lookup
  @@map("customers")
}

// Unified Contact model for the new contact form
model Contact {
  id String @id @default(cuid())

  // Basic Info
  displayName  String
  contactGroup ContactType // customer, supplier, employee
  firstName    String?
  middleName   String?
  lastName     String?

  // Contact Information
  phone     String? // Mobile phone
  telephone String? // Landline
  fax       String?
  email     String?

  // Identity Information
  identityType   String? // KTP, SIM, Passport
  identityNumber String?
  NIK            String? // Indonesian National ID
  NPWP           String? // Indonesian Tax ID

  // Company Information
  companyName String?
  otherInfo   String? // Additional info

  // Address Information
  billingAddress  String? @db.Text
  shippingAddress String? @db.Text
  sameAsShipping  Boolean @default(false)

  // Bank Information (for backward compatibility - will use first bank account)
  bankName      String?
  bankBranch    String?
  accountHolder String?
  accountNumber String?

  // Relationships
  bankAccounts ContactBankAccount[] // Multiple bank accounts

  // Additional fields
  notes String? @db.Text

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, email]) // Email is unique for a specific user (if not null)
  @@index([userId]) // Index for user lookup
  @@map("contacts")
}

enum ContactType {
  CUSTOMER
  SUPPLIER
  EMPLOYEE
}

// Contact Bank Account model for multiple bank accounts per contact
model ContactBankAccount {
  id            String   @id @default(cuid())
  bankName      String
  bankBranch    String?
  accountHolder String
  accountNumber String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relationships
  contactId String
  contact   Contact @relation(fields: [contactId], references: [id], onDelete: Cascade)

  @@index([contactId])
  @@map("contact_bank_accounts")
}

// --- Notification Models ---

enum NotificationType {
  INFO
  WARNING
  SUCCESS
  ERROR
}

model Notification {
  id        String           @id @default(cuid())
  type      NotificationType @default(INFO)
  title     String
  message   String
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relationships
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("notifications")
}

model NotificationSettings {
  id                  String   @id @default(cuid())
  emailEnabled        Boolean  @default(true)
  emailInfoEnabled    Boolean  @default(true)
  emailWarningEnabled Boolean  @default(true)
  emailSuccessEnabled Boolean  @default(true)
  emailErrorEnabled   Boolean  @default(true)
  dailySummary        Boolean  @default(false)
  weeklySummary       Boolean  @default(true)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  // Relationships
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_settings")
}

// --- Subscription and Payment Models ---

model Subscription {
  id        String           @id @default(cuid())
  plan      SubscriptionPlan
  startDate DateTime         @default(now())
  endDate   DateTime
  autoRenew Boolean          @default(false)
  status    String           @default("active") // active, canceled, expired
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relationships
  userId   String
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@index([userId])
  @@map("subscriptions")
}

model Payment {
  id            String        @id @default(cuid())
  amount        Decimal       @db.Decimal(12, 2)
  currency      String        @default("IDR")
  status        PaymentStatus @default(PENDING)
  paymentMethod String? // e.g., "credit_card", "bank_transfer", "ewallet"
  externalId    String? // ID from payment provider (Xendit)
  externalUrl   String? // Payment URL from Xendit
  invoiceId     String? // Xendit invoice ID
  paymentDate   DateTime?
  expiryDate    DateTime?
  metadata      Json? // Additional payment data
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relationships
  userId         String
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscriptionId String?
  subscription   Subscription? @relation(fields: [subscriptionId], references: [id])
  serviceId      String?
  service        Service?      @relation(fields: [serviceId], references: [id])

  @@index([userId])
  @@index([subscriptionId])
  @@index([serviceId])
  @@map("payments")
}

// --- Service Management Models ---

enum ServiceStatus {
  PENDING
  IN_PROGRESS
  WAITING_FOR_PARTS
  COMPLETED
  CANCELLED
  DELIVERED
}

enum DeviceType {
  LAPTOP
  DESKTOP
  PHONE
  TABLET
  PRINTER
  OTHER
}

model Service {
  id                      String        @id @default(cuid())
  serviceNumber           String // Service ticket number
  customerName            String // Name of the customer
  customerPhone           String // Customer phone number
  customerEmail           String? // Optional customer email
  deviceType              DeviceType    @default(OTHER)
  deviceBrand             String // Brand of the device
  deviceModel             String // Model of the device
  deviceSerialNumber      String? // Serial number of the device
  problemDescription      String        @db.Text // Description of the problem
  diagnosisNotes          String?       @db.Text // Technician's diagnosis notes
  repairNotes             String?       @db.Text // Notes about the repair process
  estimatedCost           Decimal?      @db.Decimal(12, 2) // Estimated repair cost
  finalCost               Decimal?      @db.Decimal(12, 2) // Final repair cost
  warrantyPeriod          Int?          @default(0) // Warranty period in days
  status                  ServiceStatus @default(PENDING)
  receivedDate            DateTime      @default(now()) // When the device was received
  estimatedCompletionDate DateTime? // Estimated completion date
  completedDate           DateTime? // When the repair was completed
  deliveredDate           DateTime? // When the device was returned to customer
  createdAt               DateTime      @default(now())
  updatedAt               DateTime      @updatedAt

  // Relationships
  userId         String // Foreign key to the User who recorded the service
  user           User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  customerId     String? // Optional link to a registered customer
  serviceHistory ServiceStatusHistory[] // History of status changes
  payments       Payment[] // Payments for this service

  @@index([userId])
  @@map("services")
}

model ServiceStatusHistory {
  id        String        @id @default(cuid())
  status    ServiceStatus // The status that was set
  notes     String?       @db.Text // Notes about the status change
  changedAt DateTime      @default(now()) // When the status was changed
  changedBy String // Name or ID of the person who changed the status

  // Relationships
  serviceId String // Foreign key to the Service
  service   Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@index([serviceId])
  @@map("service_status_history")
}
