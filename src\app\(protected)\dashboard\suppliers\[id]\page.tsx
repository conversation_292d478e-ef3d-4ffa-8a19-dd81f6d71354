import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SupplierDetailPage from "@/components/pages/dashboard/suppliers/detail/supplier-detail";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getSupplierById } from "@/actions/entities/suppliers";

export const metadata: Metadata = {
  title: "Detail Supplier | Kasir Online",
  description: "Detail informasi supplier",
};

interface SupplierDetailProps {
  params: {
    id: string;
  };
}

const SupplierDetail = async ({ params }: SupplierDetailProps) => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch supplier data
  const supplierResult = await getSupplierById(params.id);

  if (supplierResult.error || !supplierResult.supplier) {
    redirect("/dashboard/contacts?tab=supplier");
  }

  return (
    <DashboardLayout>
      <SupplierDetailPage supplier={supplierResult.supplier} />
    </DashboardLayout>
  );
};

export default SupplierDetail;
